#!/usr/bin/env python3
"""
Test script for SAM2 Training Pipeline GUI

This script tests the GUI application without actually launching the full interface.
It verifies that all components can be imported and initialized properly.
"""

import sys
import os

def test_gui_components():
    """Test GUI components without launching the full interface."""
    print("Testing SAM2 Training Pipeline GUI components...")
    
    try:
        # Test tkinter import
        import tkinter as tk
        print("✓ tkinter imported successfully")
        
        # Test GUI module import
        import sam2_training_pipeline_gui
        print("✓ GUI module imported successfully")
        
        # Test GUI class instantiation (without mainloop)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        app = sam2_training_pipeline_gui.SAM2TrainingPipelineGUI(root)
        print("✓ GUI application initialized successfully")
        
        # Test default paths
        print("\nDefault paths configuration:")
        for key, path in app.default_paths.items():
            exists = "✓" if os.path.exists(path) else "✗"
            print(f"  {exists} {key}: {path}")
        
        # Clean up
        root.destroy()
        
        print("\n✓ All GUI components tested successfully!")
        print("\nTo launch the full GUI, run:")
        print("  python sam2_training_pipeline_gui.py")
        print("  or double-click launch_sam2_gui.bat")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_gui_components()
    sys.exit(0 if success else 1)
