@echo off
REM SAM2 Training Pipeline GUI Launcher
REM This script launches the SAM2 Training Pipeline GUI application

echo Starting SAM2 Training Pipeline GUI...
echo.

REM Set the working directory to the script location
cd /d "%~dp0"

REM Run the GUI application using the conda environment directly
"C:\Users\<USER>\.conda\envs\sam2_env_py310\python.exe" sam2_training_pipeline_gui.py

REM Keep the window open if there's an error
if errorlevel 1 (
    echo.
    echo An error occurred. Press any key to exit...
    pause >nul
)
