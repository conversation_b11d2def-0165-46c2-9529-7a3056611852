#!/usr/bin/env python3
"""
SAM2 Training Pipeline GUI Application

A comprehensive GUI application that provides a user-friendly interface for the complete SAM2 training pipeline.
Integrates four workflow stages:
1. Video Prediction and Refinement
2. Prepare Training Data
3. Train the Model
4. VOS (Video Object Segmentation) Inference

Features:
- User-friendly interface for all configurable parameters
- Progress indicators and console output display
- Windows-specific path formatting and PowerShell environment handling
- Clear status messages and error handling for each stage
- Integration with existing SAM2 training scripts and configurations

Requirements:
- Python 3.10 with tkinter
- SAM2 environment with all dependencies
- Grounded-SAM-2 integration
- CUDA-capable GPU (recommended)

Author: Enhanced SAM2 Training Pipeline Integration
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
import json
from pathlib import Path
import time


class SAM2TrainingPipelineGUI:
    """Main GUI application for SAM2 training pipeline."""
    
    def __init__(self, root):
        """Initialize the GUI application."""
        self.root = root
        self.root.title("SAM2 Training Pipeline GUI")
        self.root.geometry("1200x800")
        
        # Default paths and settings
        self.default_paths = {
            'workspace_root': r'C:\Users\<USER>\Codings\sam2davis',
            'sam2_root': r'C:\Users\<USER>\Codings\sam2davis\sam2',
            'output_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\sam2_output',
            'datasets_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\sam2\datasets',
            'conda_env': r'C:\Users\<USER>\.conda\envs\sam2_env_py310\python.exe',
            'checkpoints_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\checkpoints',
            'logs_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints'
        }
        
        # Process tracking
        self.current_process = None
        self.process_thread = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs for each stage
        self.create_stage1_tab()
        self.create_stage2_tab()
        self.create_stage3_tab()
        self.create_stage4_tab()
        
        # Create console output area
        self.create_console_area()
        
    def create_stage1_tab(self):
        """Create Stage 1: Video Prediction and Refinement tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Stage 1: Video Prediction")
        
        # Input type selection
        input_frame = ttk.LabelFrame(frame, text="Input Selection", padding=10)
        input_frame.pack(fill='x', padx=5, pady=5)
        
        self.input_type = tk.StringVar(value="video")
        ttk.Radiobutton(input_frame, text="Video File", variable=self.input_type, value="video").pack(anchor='w')
        ttk.Radiobutton(input_frame, text="Frame Directory", variable=self.input_type, value="frames").pack(anchor='w')
        
        # Video input
        video_frame = ttk.LabelFrame(frame, text="Video Input", padding=10)
        video_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(video_frame, text="Video Path:").grid(row=0, column=0, sticky='w', padx=5)
        self.video_path = tk.StringVar(value=r"C:\Users\<USER>\Codings\sam2davis\Grounded-SAM-2\assets\bedroom.mp4")
        ttk.Entry(video_frame, textvariable=self.video_path, width=60).grid(row=0, column=1, padx=5)
        ttk.Button(video_frame, text="Browse", command=self.browse_video_file).grid(row=0, column=2, padx=5)
        
        # Frame directory input
        frames_frame = ttk.LabelFrame(frame, text="Frame Directory Input", padding=10)
        frames_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(frames_frame, text="Frames Directory:").grid(row=0, column=0, sticky='w', padx=5)
        self.frames_dir = tk.StringVar(value=r"C:\Users\<USER>\Codings\sam2davis\sam2\datasets\DAVIS\JPEGImages\480p\shooting")
        ttk.Entry(frames_frame, textvariable=self.frames_dir, width=60).grid(row=0, column=1, padx=5)
        ttk.Button(frames_frame, text="Browse", command=self.browse_frames_dir).grid(row=0, column=2, padx=5)
        
        # Parameters
        params_frame = ttk.LabelFrame(frame, text="Parameters", padding=10)
        params_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(params_frame, text="Text Prompt:").grid(row=0, column=0, sticky='w', padx=5)
        self.text_prompt = tk.StringVar(value="bedroom.")
        ttk.Entry(params_frame, textvariable=self.text_prompt, width=30).grid(row=0, column=1, padx=5)
        
        ttk.Label(params_frame, text="Model:").grid(row=0, column=2, sticky='w', padx=5)
        self.model_type = tk.StringVar(value="custom")
        model_combo = ttk.Combobox(params_frame, textvariable=self.model_type, values=["tiny", "small", "base_plus", "large", "custom"])
        model_combo.grid(row=0, column=3, padx=5)
        
        ttk.Label(params_frame, text="Frame Step:").grid(row=1, column=0, sticky='w', padx=5)
        self.frame_step = tk.StringVar(value="100")
        ttk.Entry(params_frame, textvariable=self.frame_step, width=10).grid(row=1, column=1, padx=5)
        
        self.allow_refinement = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_frame, text="Allow Refinement", variable=self.allow_refinement).grid(row=1, column=2, columnspan=2, sticky='w', padx=5)
        
        # Output directory
        output_frame = ttk.LabelFrame(frame, text="Output", padding=10)
        output_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(output_frame, text="Output Directory:").grid(row=0, column=0, sticky='w', padx=5)
        self.stage1_output = tk.StringVar(value=self.default_paths['output_dir'])
        ttk.Entry(output_frame, textvariable=self.stage1_output, width=60).grid(row=0, column=1, padx=5)
        ttk.Button(output_frame, text="Browse", command=self.browse_stage1_output).grid(row=0, column=2, padx=5)
        
        # Run button
        ttk.Button(frame, text="Run Video Prediction", command=self.run_stage1).pack(pady=10)
        
    def create_stage2_tab(self):
        """Create Stage 2: Prepare Training Data tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Stage 2: Prepare Data")
        
        # Input/Output directories
        dirs_frame = ttk.LabelFrame(frame, text="Directories", padding=10)
        dirs_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(dirs_frame, text="Input Directory:").grid(row=0, column=0, sticky='w', padx=5)
        self.stage2_input = tk.StringVar(value=self.default_paths['output_dir'])
        ttk.Entry(dirs_frame, textvariable=self.stage2_input, width=60).grid(row=0, column=1, padx=5)
        ttk.Button(dirs_frame, text="Browse", command=self.browse_stage2_input).grid(row=0, column=2, padx=5)
        
        ttk.Label(dirs_frame, text="Output Directory:").grid(row=1, column=0, sticky='w', padx=5)
        self.stage2_output = tk.StringVar(value=self.default_paths['datasets_dir'])
        ttk.Entry(dirs_frame, textvariable=self.stage2_output, width=60).grid(row=1, column=1, padx=5)
        ttk.Button(dirs_frame, text="Browse", command=self.browse_stage2_output).grid(row=1, column=2, padx=5)
        
        # Options
        options_frame = ttk.LabelFrame(frame, text="Options", padding=10)
        options_frame.pack(fill='x', padx=5, pady=5)
        
        self.auto_detect = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Auto-detect video sequences", variable=self.auto_detect).pack(anchor='w')
        
        # Run button
        ttk.Button(frame, text="Prepare Training Data", command=self.run_stage2).pack(pady=10)
        
    def create_stage3_tab(self):
        """Create Stage 3: Train the Model tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Stage 3: Train Model")
        
        # Configuration
        config_frame = ttk.LabelFrame(frame, text="Training Configuration", padding=10)
        config_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(config_frame, text="Config File:").grid(row=0, column=0, sticky='w', padx=5)
        self.config_file = tk.StringVar(value="sam2/configs/sam2.1_training/sam2.1_hiera_b+_CUSTOM_8GB.yaml")
        ttk.Entry(config_frame, textvariable=self.config_file, width=60).grid(row=0, column=1, padx=5)
        ttk.Button(config_frame, text="Browse", command=self.browse_config_file).grid(row=0, column=2, padx=5)
        
        ttk.Label(config_frame, text="Number of GPUs:").grid(row=1, column=0, sticky='w', padx=5)
        self.num_gpus = tk.StringVar(value="1")
        ttk.Entry(config_frame, textvariable=self.num_gpus, width=10).grid(row=1, column=1, sticky='w', padx=5)
        
        # Environment
        env_frame = ttk.LabelFrame(frame, text="Environment", padding=10)
        env_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(env_frame, text="Python Executable:").grid(row=0, column=0, sticky='w', padx=5)
        self.python_exe = tk.StringVar(value=self.default_paths['conda_env'])
        ttk.Entry(env_frame, textvariable=self.python_exe, width=60).grid(row=0, column=1, padx=5)
        ttk.Button(env_frame, text="Browse", command=self.browse_python_exe).grid(row=0, column=2, padx=5)
        
        # Run button
        ttk.Button(frame, text="Start Training", command=self.run_stage3).pack(pady=10)
        
    def create_stage4_tab(self):
        """Create Stage 4: VOS Inference tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Stage 4: VOS Inference")
        
        # Checkpoint management
        checkpoint_frame = ttk.LabelFrame(frame, text="Checkpoint Management", padding=10)
        checkpoint_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(checkpoint_frame, text="Source Checkpoints:").grid(row=0, column=0, sticky='w', padx=5)
        self.source_checkpoints = tk.StringVar(value=self.default_paths['logs_dir'])
        ttk.Entry(checkpoint_frame, textvariable=self.source_checkpoints, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(checkpoint_frame, text="Browse", command=self.browse_source_checkpoints).grid(row=0, column=2, padx=5)
        
        ttk.Label(checkpoint_frame, text="Target Checkpoints:").grid(row=1, column=0, sticky='w', padx=5)
        self.target_checkpoints = tk.StringVar(value=self.default_paths['checkpoints_dir'])
        ttk.Entry(checkpoint_frame, textvariable=self.target_checkpoints, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(checkpoint_frame, text="Browse", command=self.browse_target_checkpoints).grid(row=1, column=2, padx=5)
        
        ttk.Button(checkpoint_frame, text="Copy Checkpoints", command=self.copy_checkpoints).grid(row=2, column=1, pady=5)
        
        # Dataset management
        dataset_frame = ttk.LabelFrame(frame, text="Dataset Management", padding=10)
        dataset_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(dataset_frame, text="Source Dataset:").grid(row=0, column=0, sticky='w', padx=5)
        self.source_dataset = tk.StringVar(value=os.path.join(self.default_paths['datasets_dir'], 'CUSTOM'))
        ttk.Entry(dataset_frame, textvariable=self.source_dataset, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(dataset_frame, text="Browse", command=self.browse_source_dataset).grid(row=0, column=2, padx=5)
        
        ttk.Label(dataset_frame, text="Target Dataset:").grid(row=1, column=0, sticky='w', padx=5)
        self.target_dataset = tk.StringVar(value=os.path.join(self.default_paths['sam2_root'], 'datasets'))
        ttk.Entry(dataset_frame, textvariable=self.target_dataset, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(dataset_frame, text="Browse", command=self.browse_target_dataset).grid(row=1, column=2, padx=5)
        
        ttk.Button(dataset_frame, text="Copy Dataset", command=self.copy_dataset).grid(row=2, column=1, pady=5)
        
        # VOS Inference parameters
        vos_frame = ttk.LabelFrame(frame, text="VOS Inference Parameters", padding=10)
        vos_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(vos_frame, text="SAM2 Config:").grid(row=0, column=0, sticky='w', padx=5)
        self.sam2_cfg = tk.StringVar(value="configs/sam2.1/sam2.1_hiera_b+.yaml")
        ttk.Entry(vos_frame, textvariable=self.sam2_cfg, width=40).grid(row=0, column=1, padx=5)
        
        ttk.Label(vos_frame, text="Checkpoint:").grid(row=1, column=0, sticky='w', padx=5)
        self.sam2_checkpoint = tk.StringVar(value="./checkpoints/checkpoint.pt")
        ttk.Entry(vos_frame, textvariable=self.sam2_checkpoint, width=40).grid(row=1, column=1, padx=5)
        
        ttk.Label(vos_frame, text="Output Directory:").grid(row=2, column=0, sticky='w', padx=5)
        self.vos_output = tk.StringVar(value="./outputs/custom_pred_pngs")
        ttk.Entry(vos_frame, textvariable=self.vos_output, width=40).grid(row=2, column=1, padx=5)
        
        # Run button
        ttk.Button(frame, text="Run VOS Inference", command=self.run_stage4).pack(pady=10)
        
    def create_console_area(self):
        """Create console output area."""
        console_frame = ttk.LabelFrame(self.root, text="Console Output", padding=5)
        console_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        self.console_text = scrolledtext.ScrolledText(console_frame, height=15, wrap=tk.WORD)
        self.console_text.pack(fill='both', expand=True)
        
        # Progress bar
        self.progress = ttk.Progressbar(console_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=(5, 0))
        
        # Control buttons
        control_frame = ttk.Frame(console_frame)
        control_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Button(control_frame, text="Clear Console", command=self.clear_console).pack(side='left', padx=5)
        ttk.Button(control_frame, text="Stop Process", command=self.stop_process).pack(side='left', padx=5)

    # Browse methods
    def browse_video_file(self):
        """Browse for video file."""
        filename = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        if filename:
            self.video_path.set(filename)

    def browse_frames_dir(self):
        """Browse for frames directory."""
        dirname = filedialog.askdirectory(title="Select Frames Directory")
        if dirname:
            self.frames_dir.set(dirname)

    def browse_stage1_output(self):
        """Browse for stage 1 output directory."""
        dirname = filedialog.askdirectory(title="Select Output Directory")
        if dirname:
            self.stage1_output.set(dirname)

    def browse_stage2_input(self):
        """Browse for stage 2 input directory."""
        dirname = filedialog.askdirectory(title="Select Input Directory")
        if dirname:
            self.stage2_input.set(dirname)

    def browse_stage2_output(self):
        """Browse for stage 2 output directory."""
        dirname = filedialog.askdirectory(title="Select Output Directory")
        if dirname:
            self.stage2_output.set(dirname)

    def browse_config_file(self):
        """Browse for config file."""
        filename = filedialog.askopenfilename(
            title="Select Config File",
            filetypes=[("YAML files", "*.yaml *.yml"), ("All files", "*.*")]
        )
        if filename:
            self.config_file.set(filename)

    def browse_python_exe(self):
        """Browse for Python executable."""
        filename = filedialog.askopenfilename(
            title="Select Python Executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if filename:
            self.python_exe.set(filename)

    def browse_source_checkpoints(self):
        """Browse for source checkpoints directory."""
        dirname = filedialog.askdirectory(title="Select Source Checkpoints Directory")
        if dirname:
            self.source_checkpoints.set(dirname)

    def browse_target_checkpoints(self):
        """Browse for target checkpoints directory."""
        dirname = filedialog.askdirectory(title="Select Target Checkpoints Directory")
        if dirname:
            self.target_checkpoints.set(dirname)

    def browse_source_dataset(self):
        """Browse for source dataset directory."""
        dirname = filedialog.askdirectory(title="Select Source Dataset Directory")
        if dirname:
            self.source_dataset.set(dirname)

    def browse_target_dataset(self):
        """Browse for target dataset directory."""
        dirname = filedialog.askdirectory(title="Select Target Dataset Directory")
        if dirname:
            self.target_dataset.set(dirname)

    # Console methods
    def log_message(self, message):
        """Log a message to the console."""
        self.console_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.console_text.see(tk.END)
        self.root.update_idletasks()

    def clear_console(self):
        """Clear the console output."""
        self.console_text.delete(1.0, tk.END)

    def stop_process(self):
        """Stop the current running process."""
        if self.current_process:
            try:
                self.current_process.terminate()
                self.log_message("Process terminated by user")
            except:
                self.log_message("Failed to terminate process")
        self.progress.stop()

    # Stage execution methods
    def run_stage1(self):
        """Run Stage 1: Video Prediction and Refinement."""
        self.log_message("Starting Stage 1: Video Prediction and Refinement")
        self.progress.start()

        # Build command based on input type
        if self.input_type.get() == "video":
            input_arg = f'--video "{self.video_path.get()}"'
        else:
            input_arg = f'--frames-dir "{self.frames_dir.get()}"'

        # Build command
        cmd_parts = [
            f'cd "{self.default_paths["sam2_root"]}"',
            '&&',
            f'"{self.python_exe.get()}"',
            '../video_predictor_sam2_v8.py',
            input_arg,
            f'--text-prompt "{self.text_prompt.get()}"',
            f'--model {self.model_type.get()}'
        ]

        if self.frame_step.get():
            cmd_parts.append(f'--frame-step {self.frame_step.get()}')

        if self.allow_refinement.get():
            cmd_parts.append('--allow-refinement')

        command = ' '.join(cmd_parts)

        # Run in thread
        self.process_thread = threading.Thread(target=self._run_command, args=(command, "Stage 1"))
        self.process_thread.start()

    def run_stage2(self):
        """Run Stage 2: Prepare Training Data."""
        self.log_message("Starting Stage 2: Prepare Training Data")
        self.progress.start()

        # Build command
        cmd_parts = [
            f'cd "{self.default_paths["workspace_root"]}"',
            '&&',
            f'"{self.python_exe.get()}"',
            'custom_data_prep.py',
            f'--input_dir "{self.stage2_input.get()}"',
            f'--output_dir "{self.stage2_output.get()}"'
        ]

        if self.auto_detect.get():
            cmd_parts.append('--auto-detect')

        command = ' '.join(cmd_parts)

        # Run in thread
        self.process_thread = threading.Thread(target=self._run_command, args=(command, "Stage 2"))
        self.process_thread.start()

    def run_stage3(self):
        """Run Stage 3: Train the Model."""
        self.log_message("Starting Stage 3: Train the Model")
        self.progress.start()

        # Set PYTHONPATH and run training
        pythonpath = f"{self.default_paths['sam2_root']};{self.default_paths['workspace_root']}"

        # Build PowerShell command with environment variable
        cmd_parts = [
            f'$env:PYTHONPATH = "{pythonpath};$env:PYTHONPATH"',
            ';',
            f'cd "{self.default_paths["sam2_root"]}"',
            ';',
            f'& "{self.python_exe.get()}"',
            'training/train.py',
            f'-c {self.config_file.get()}',
            '--use-cluster 0',
            f'--num-gpus {self.num_gpus.get()}'
        ]

        command = ' '.join(cmd_parts)

        # Run in thread with PowerShell
        self.process_thread = threading.Thread(target=self._run_powershell_command, args=(command, "Stage 3"))
        self.process_thread.start()

    def copy_checkpoints(self):
        """Copy checkpoints from training logs to checkpoints directory."""
        self.log_message("Copying checkpoints...")

        try:
            import shutil
            source = self.source_checkpoints.get()
            target = self.target_checkpoints.get()

            if not os.path.exists(source):
                self.log_message(f"Error: Source directory does not exist: {source}")
                return

            os.makedirs(target, exist_ok=True)

            # Copy all checkpoint files
            for file in os.listdir(source):
                if file.endswith('.pt'):
                    src_file = os.path.join(source, file)
                    dst_file = os.path.join(target, file)
                    shutil.copy2(src_file, dst_file)
                    self.log_message(f"Copied: {file}")

            self.log_message("Checkpoints copied successfully!")

        except Exception as e:
            self.log_message(f"Error copying checkpoints: {str(e)}")

    def copy_dataset(self):
        """Copy dataset from source to target directory."""
        self.log_message("Copying dataset...")

        try:
            import shutil
            source = self.source_dataset.get()
            target = os.path.join(self.target_dataset.get(), 'CUSTOM')

            if not os.path.exists(source):
                self.log_message(f"Error: Source directory does not exist: {source}")
                return

            if os.path.exists(target):
                shutil.rmtree(target)

            shutil.copytree(source, target)
            self.log_message("Dataset copied successfully!")

        except Exception as e:
            self.log_message(f"Error copying dataset: {str(e)}")

    def run_stage4(self):
        """Run Stage 4: VOS Inference."""
        self.log_message("Starting Stage 4: VOS Inference")
        self.progress.start()

        # Build command
        cmd_parts = [
            f'cd "{self.default_paths["sam2_root"]}"',
            '&&',
            f'"{self.python_exe.get()}"',
            './tools/vos_inference.py',
            f'--sam2_cfg {self.sam2_cfg.get()}',
            f'--sam2_checkpoint {self.sam2_checkpoint.get()}',
            '--base_video_dir ./datasets/CUSTOM/JPEGImages/480p',
            '--input_mask_dir ./datasets/CUSTOM/Annotations/480p',
            '--video_list_file ./datasets/CUSTOM/ImageSets/val_all.txt',
            f'--output_mask_dir {self.vos_output.get()}'
        ]

        command = ' '.join(cmd_parts)

        # Run in thread
        self.process_thread = threading.Thread(target=self._run_command, args=(command, "Stage 4"))
        self.process_thread.start()

    def _run_command(self, command, stage_name):
        """Run a command in a subprocess and capture output."""
        try:
            self.log_message(f"Executing command: {command}")

            # Use shell=True for Windows compatibility
            self.current_process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Read output line by line
            for line in iter(self.current_process.stdout.readline, ''):
                if line:
                    self.log_message(line.strip())

            self.current_process.wait()

            if self.current_process.returncode == 0:
                self.log_message(f"{stage_name} completed successfully!")
            else:
                self.log_message(f"{stage_name} failed with return code: {self.current_process.returncode}")

        except Exception as e:
            self.log_message(f"Error running {stage_name}: {str(e)}")
        finally:
            self.progress.stop()
            self.current_process = None

    def _run_powershell_command(self, command, stage_name):
        """Run a PowerShell command in a subprocess and capture output."""
        try:
            self.log_message(f"Executing PowerShell command: {command}")

            # Use PowerShell to execute the command
            ps_command = ['powershell', '-Command', command]

            self.current_process = subprocess.Popen(
                ps_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Read output line by line
            for line in iter(self.current_process.stdout.readline, ''):
                if line:
                    self.log_message(line.strip())

            self.current_process.wait()

            if self.current_process.returncode == 0:
                self.log_message(f"{stage_name} completed successfully!")
            else:
                self.log_message(f"{stage_name} failed with return code: {self.current_process.returncode}")

        except Exception as e:
            self.log_message(f"Error running {stage_name}: {str(e)}")
        finally:
            self.progress.stop()
            self.current_process = None


def main():
    """Main function to run the GUI application."""
    root = tk.Tk()
    app = SAM2TrainingPipelineGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
