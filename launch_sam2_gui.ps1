# SAM2 Training Pipeline GUI Launcher (PowerShell)
# This script launches the SAM2 Training Pipeline GUI application

Write-Host "Starting SAM2 Training Pipeline GUI..." -ForegroundColor Green
Write-Host ""

# Set the working directory to the script location
Set-Location -Path $PSScriptRoot

# Set environment variables for SAM2
$env:PYTHONPATH = "C:\Users\<USER>\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;$env:PYTHONPATH"

try {
    # Run the GUI application using the conda environment
    & "C:\Users\<USER>\.conda\envs\sam2_env_py310\python.exe" sam2_training_pipeline_gui.py
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "GUI application closed successfully." -ForegroundColor Green
    } else {
        Write-Host "GUI application exited with error code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "Error launching GUI application: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey("NoE<PERSON>,IncludeKeyDown")
}
