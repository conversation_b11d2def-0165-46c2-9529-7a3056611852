# SAM2 Training Pipeline GUI

A comprehensive GUI application that provides a user-friendly interface for the complete SAM2 training pipeline. This application integrates all four workflow stages into a single, easy-to-use interface.

## Features

- **Stage 1: Video Prediction and Refinement** - Run video prediction using the enhanced video predictor
- **Stage 2: Prepare Training Data** - Convert prediction results to DAVIS-compatible training format
- **Stage 3: Train the Model** - Execute SAM2 training with custom configurations
- **Stage 4: VOS Inference** - Run Video Object Segmentation inference with trained models
- **Real-time Console Output** - Monitor progress and debug issues
- **Windows-Compatible** - Handles PowerShell environment variables and Windows paths
- **Progress Indicators** - Visual feedback for long-running operations

## Requirements

- Python 3.10 with tkinter support
- SAM2 environment with all dependencies installed
- Grounded-SAM-2 integration
- CUDA-capable GPU (recommended)
- Windows 10/11 with PowerShell

## Installation

1. Ensure your SAM2 environment is properly set up:
   ```bash
   conda activate sam2_env_py310
   ```

2. Verify tkinter is available:
   ```python
   import tkinter
   print("tkinter is available")
   ```

3. Place the GUI files in your SAM2 workspace root:
   ```
   C:\Users\<USER>\Codings\sam2davis\
   ├── sam2_training_pipeline_gui.py
   ├── launch_sam2_gui.bat
   └── SAM2_GUI_README.md
   ```

## Usage

### Quick Start

1. **Launch the GUI:**
   - Double-click `launch_sam2_gui.bat`, or
   - Run manually: `python sam2_training_pipeline_gui.py`

2. **Follow the workflow stages in order:**
   - Stage 1: Generate video predictions
   - Stage 2: Prepare training data
   - Stage 3: Train the model
   - Stage 4: Run VOS inference

### Stage 1: Video Prediction and Refinement

**Purpose:** Generate initial object segmentations using text prompts or manual annotation.

**Inputs:**
- **Video File:** Path to input video (e.g., `bedroom.mp4`)
- **Frame Directory:** Alternative to video file - directory containing frame images
- **Text Prompt:** Description of objects to detect (e.g., "bedroom.", "gun.")
- **Model:** SAM2 model size (tiny, small, base_plus, large, custom)
- **Frame Step:** Sampling interval for frames (default: 100)
- **Allow Refinement:** Enable interactive refinement capabilities

**Default Command:**
```bash
cd sam2
python video_predictor_sam2_v8.py --video "C:/Users/<USER>/Codings/sam2davis/Grounded-SAM-2/assets/bedroom.mp4" --text-prompt "bedroom." --allow-refinement --model custom --frame-step 100
```

**Output:** Segmentation masks and metadata in `C:\Users\<USER>\Codings\sam2davis\sam2\sam2_output\`

### Stage 2: Prepare Training Data

**Purpose:** Convert video prediction results to DAVIS-compatible format for training.

**Inputs:**
- **Input Directory:** Source folder with prediction results (default: sam2_output)
- **Output Directory:** Target folder for DAVIS-formatted data (default: sam2/datasets)
- **Auto-detect:** Automatically detect video sequences in input directory

**Default Command:**
```bash
python custom_data_prep.py --input_dir "C:\Users\<USER>\Codings\sam2davis\sam2\sam2_output\shooting" --output_dir "C:\Users\<USER>\Codings\sam2davis\sam2\sam2\datasets" --auto-detect
```

**Output:** DAVIS-formatted dataset in `datasets/CUSTOM/` with proper structure

### Stage 3: Train the Model

**Purpose:** Train SAM2 model on custom dataset using optimized configuration.

**Inputs:**
- **Config File:** Training configuration (default: `sam2.1_hiera_b+_CUSTOM_8GB.yaml`)
- **Number of GPUs:** GPU count for training (default: 1)
- **Python Executable:** Path to conda environment Python

**Default Command:**
```powershell
$env:PYTHONPATH = "C:\Users\<USER>\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;$env:PYTHONPATH"
cd C:\Users\<USER>\Codings\sam2davis\sam2
python training/train.py -c sam2/configs/sam2.1_training/sam2.1_hiera_b+_CUSTOM_8GB.yaml --use-cluster 0 --num-gpus 1
```

**Output:** Trained model checkpoints in `sam2_logs/` directory

### Stage 4: VOS Inference

**Purpose:** Run Video Object Segmentation inference using trained model.

**Setup Steps:**
1. **Copy Checkpoints:** Move trained checkpoints to inference directory
2. **Copy Dataset:** Move dataset to inference location
3. **Run Inference:** Execute VOS inference with trained model

**Default Command:**
```bash
python ./tools/vos_inference.py --sam2_cfg configs/sam2.1/sam2.1_hiera_b+.yaml --sam2_checkpoint ./checkpoints/checkpoint.pt --base_video_dir ./datasets/CUSTOM/JPEGImages/480p --input_mask_dir ./datasets/CUSTOM/Annotations/480p --video_list_file ./datasets/CUSTOM/ImageSets/val_all.txt --output_mask_dir ./outputs/custom_pred_pngs
```

**Output:** Inference results in `outputs/custom_pred_pngs/`

## Configuration

### Default Paths
The GUI uses these default paths (configurable in the interface):

```python
default_paths = {
    'workspace_root': r'C:\Users\<USER>\Codings\sam2davis',
    'sam2_root': r'C:\Users\<USER>\Codings\sam2davis\sam2',
    'output_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\sam2_output',
    'datasets_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\sam2\datasets',
    'conda_env': r'C:\Users\<USER>\.conda\envs\sam2_env_py310\python.exe',
    'checkpoints_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\checkpoints',
    'logs_dir': r'C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints'
}
```

### Training Configuration
The GUI uses the optimized 8GB VRAM configuration by default:
- **Resolution:** 512px (reduced for memory efficiency)
- **Batch Size:** 1 with 16 gradient accumulation steps
- **Learning Rates:** Base 1.0e-5, Vision 6.0e-6
- **Epochs:** 25 with extended training for better convergence

## Troubleshooting

### Common Issues

1. **"tkinter not found"**
   - Install tkinter: `conda install tk` or use system Python with tkinter

2. **"Permission denied" errors**
   - Run as administrator or check file permissions
   - Ensure directories are writable

3. **CUDA out of memory**
   - Reduce batch size or resolution in training config
   - Close other GPU-intensive applications

4. **PowerShell execution policy**
   - Run: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

5. **Path not found errors**
   - Verify all paths in the GUI match your actual directory structure
   - Use the Browse buttons to select correct paths

### Console Output
Monitor the console output area for:
- Command execution details
- Error messages and stack traces
- Progress updates
- Completion status

### Process Control
- **Stop Process:** Terminate currently running operation
- **Clear Console:** Clear output history for better readability

## Advanced Usage

### Custom Configurations
- Modify training configurations in `sam2/configs/sam2.1_training/`
- Adjust memory settings for different GPU configurations
- Customize dataset preparation parameters

### Batch Processing
- Use the GUI to set up commands, then run them in batch scripts
- Monitor multiple training runs using TensorBoard

### Integration
- The GUI can be extended to support additional SAM2 workflows
- Commands can be exported for use in automated pipelines

## Support

For issues and questions:
1. Check the console output for detailed error messages
2. Verify environment setup and dependencies
3. Ensure all required files and directories exist
4. Review the SAM2 and Grounded-SAM-2 documentation

## Author

Enhanced SAM2 Training Pipeline Integration
Based on Meta's SAM2 framework with Grounded-SAM-2 enhancements
